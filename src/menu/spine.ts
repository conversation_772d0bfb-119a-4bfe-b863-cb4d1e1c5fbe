import { type RouteVO } from "@/api/system/menu";

const routes: RouteVO[] = [
  {
    path: "/spine",
    component: "Layout",
    name: "SpinalScreening",
    sort: 120,
    redirect: "/spine/memberManagement",
    meta: {
      title: "脊柱筛查项目",
      hidden: false,
      roles: ["superAdmin", "assistant", "spinalScreeningDataQuery"],
    },
    children: [
      {
        path: "dataBoard",
        name: "SpinalScreeningDataBoard",
        component: "spine/dataBoard/index",
        meta: {
          title: "大数据看板",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["spinalScreeningDataQuery"],
        },
      },
      {
        path: "overallData",
        name: "SpinalScreeningOverallData",
        component: "spine/overallData/index",
        meta: {
          title: "总体数据",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["spinalScreeningDataQuery"],
        },
      },
      {
        path: "studentManagement",
        name: "SpinalScreeningStudentManagement",
        component: "spine/studentManagement/index",
        meta: {
          title: "学生管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["spinalScreeningDataQuery"],
        },
      },
      {
        path: "schoolData",
        name: "SpinalScreeningSchoolData",
        component: "spine/schoolData/index",
        meta: {
          title: "按学校查看",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["spinalScreeningDataQuery"],
        },
      },
      {
        path: "missionManagement",
        name: "SpinalScreeningMissionManagement",
        component: "spine/missionManagement/index",
        meta: {
          title: "宣教推荐设置",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superAdmin", "assistant"],
        },
      },
      {
        path: "memberManagement",
        name: "SpinalScreeningMemberManagement",
        component: "spine/memberManagement/index",
        meta: {
          title: "成员管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superAdmin", "assistant"],
        },
      },
    ],
  },
];
export default routes;
