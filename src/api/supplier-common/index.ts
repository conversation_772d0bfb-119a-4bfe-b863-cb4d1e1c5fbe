import request from "@/utils/request";
import {
  type GlobalStatisticItem,
  type GlobalStatisticsInputDTO,
  type IkidAndFootItem,
} from "./types";

const Supplier_Common_Common = "/supplier-common/api/common";
const Supplier_Common_Api = {
  /** 获取脊柱大屏数据 */
  ikidAndFootStatistics(data: { StartDate: string; EndDate: string }) {
    return request.post<IkidAndFootItem>(`${Supplier_Common_Common}/IkidAndFootStatistics`, data);
  },
  /** 获取总体数据 */
  globalStatistics(data: GlobalStatisticsInputDTO) {
    return request.post<ListDataTotal<GlobalStatisticItem>>(
      `${Supplier_Common_Common}/GlobalStatistics`,
      data
    );
  },
};

export default Supplier_Common_Api;
