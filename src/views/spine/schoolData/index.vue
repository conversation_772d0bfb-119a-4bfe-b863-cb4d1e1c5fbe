<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                  :shortcuts="pageDatePickerShortcuts"
                />
              </el-form-item>
              <el-form-item label="学校">
                <el-input v-model="queryParams.School" placeholder="请输入" clearable />
              </el-form-item>
              <el-form-item label="年级">
                <el-select
                  v-model="queryParams.Grade"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="一年级" value="一年级" />
                  <el-option label="二年级" value="二年级" />
                  <el-option label="三年级" value="三年级" />
                  <el-option label="四年级" value="四年级" />
                  <el-option label="五年级" value="五年级" />
                  <el-option label="六年级" value="六年级" />
                </el-select>
              </el-form-item>
              <el-form-item label="班级">
                <el-input v-model="queryParams.Class" placeholder="请输入" clearable />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="rawData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          default-expand-all
          style="flex: 1; text-align: center"
        >
          <el-table-column :label="showLabelName" align="left">
            <template #default="scope">
              {{ scope.row.Name }}
            </template>
          </el-table-column>
          <el-table-column prop="ScreeningCount" label="筛查人数" align="center" />
          <el-table-column prop="ScreeningCount" label="脊柱侧弯人数/比例" align="center">
            <template #default="scope">
              <div>{{ scope.row.SpineCount + "/" + scope.row.SpineRate }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="ScreeningCount" label="脊柱侧弯分布人数/比例" align="center">
            <template #default="scope">
              <div>低风险{{ scope.row.LowRiskCount + "/" + scope.row.LowRiskRate }}</div>
              <div>高风险{{ scope.row.HighRiskCount + "/" + scope.row.HighRiskRate }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="ScreeningCount" label="足弓异常人数/比例" align="center">
            <template #default="scope">
              <div>{{ scope.row.FootCount + "/" + scope.row.FootRate }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="ScreeningCount" label="左足弓异常分布人数/比例" align="center">
            <template #default="scope">
              <div>高足弓{{ scope.row.LeftHighCount + "/" + scope.row.LeftHighRate }}</div>
              <div>轻度低足弓{{ scope.row.LeftLightCount + "/" + scope.row.LeftLightRate }}</div>
              <div>中度低足弓{{ scope.row.LeftMediumCount + "/" + scope.row.LeftMediumRate }}</div>
              <div>重度低足弓{{ scope.row.LeftHeavyCount + "/" + scope.row.LeftHeavyRate }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="ScreeningCount" label="右足弓异常分布人数/比例" align="center">
            <template #default="scope">
              <div>高足弓{{ scope.row.RightHighCount + "/" + scope.row.RightHighRate }}</div>
              <div>轻度低足弓{{ scope.row.RightLightCount + "/" + scope.row.RightLightRate }}</div>
              <div>
                中度低足弓{{ scope.row.RightMediumCount + "/" + scope.row.RightMediumRate }}
              </div>
              <div>重度低足弓{{ scope.row.RightHeavyCount + "/" + scope.row.RightHeavyRate }}</div>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";
const { datePickerShortcuts } = useDateRangePicker();
const pageDatePickerShortcuts = [
  ...datePickerShortcuts,
  {
    text: "2025年上半年",
    value() {
      const end = "2025-06-30 23:59:59";
      const start = "2025-05-26 00:00:00";
      return [start, end];
    },
  },
];

defineOptions({
  name: "SpinalScreeningSchoolData",
});

const queryParams = ref<any>({
  PageIndex: 1,
  PageSize: 20,
  StartDate: dayjs().format("YYYY-MM-01 00:00:00"),
  EndDate: dayjs().format("YYYY-MM-DD 23:59:59"),
  School: null,
  Grade: null,
  Class: null,
  Keyword: "",
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const showLabelName = ref<string>("学校");

// 原始数据
const rawData = ref<any[]>([]);

const {
  tableLoading,
  pageData,
  total,
  tableRef,
  exportLoading,
  tableDateFormat,
  tableFluidHeight,
  tableResize,
} = useTableConfig<unknown>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  showLabelName.value = handleGetLabel();
  handleGetTableList();
};
const handleGetTableList = async () => {
  tableLoading.value = true;

  try {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 模拟测试数据
    const mockData = [
      {
        Id: "1",
        Name: "北京市第一小学",
        ScreeningCount: 1856,
        SpineCount: 89,
        SpineRate: "4.8%",
        LowRiskCount: 67,
        LowRiskRate: "3.6%",
        HighRiskCount: 22,
        HighRiskRate: "1.2%",
        FootCount: 156,
        FootRate: "8.4%",
        LeftHighCount: 34,
        LeftHighRate: "1.8%",
        LeftLightCount: 45,
        LeftLightRate: "2.4%",
        LeftMediumCount: 28,
        LeftMediumRate: "1.5%",
        LeftHeavyCount: 12,
        LeftHeavyRate: "0.6%",
        RightHighCount: 32,
        RightHighRate: "1.7%",
        RightLightCount: 41,
        RightLightRate: "2.2%",
        RightMediumCount: 26,
        RightMediumRate: "1.4%",
        RightHeavyCount: 14,
        RightHeavyRate: "0.8%",
        hasChildren: true,
        children: [
          {
            Id: "1-1",
            Name: "男",
            ScreeningCount: 942,
            SpineCount: 45,
            SpineRate: "4.8%",
            LowRiskCount: 34,
            LowRiskRate: "3.6%",
            HighRiskCount: 11,
            HighRiskRate: "1.2%",
            FootCount: 79,
            FootRate: "8.4%",
            LeftHighCount: 17,
            LeftHighRate: "1.8%",
            LeftLightCount: 23,
            LeftLightRate: "2.4%",
            LeftMediumCount: 14,
            LeftMediumRate: "1.5%",
            LeftHeavyCount: 6,
            LeftHeavyRate: "0.6%",
            RightHighCount: 16,
            RightHighRate: "1.7%",
            RightLightCount: 21,
            RightLightRate: "2.2%",
            RightMediumCount: 13,
            RightMediumRate: "1.4%",
            RightHeavyCount: 7,
            RightHeavyRate: "0.7%",
            hasChildren: false,
            children: [],
          },
          {
            Id: "1-2",
            Name: "女",
            ScreeningCount: 914,
            SpineCount: 44,
            SpineRate: "4.8%",
            LowRiskCount: 33,
            LowRiskRate: "3.6%",
            HighRiskCount: 11,
            HighRiskRate: "1.2%",
            FootCount: 77,
            FootRate: "8.4%",
            LeftHighCount: 17,
            LeftHighRate: "1.9%",
            LeftLightCount: 22,
            LeftLightRate: "2.4%",
            LeftMediumCount: 14,
            LeftMediumRate: "1.5%",
            LeftHeavyCount: 6,
            LeftHeavyRate: "0.7%",
            RightHighCount: 16,
            RightHighRate: "1.8%",
            RightLightCount: 20,
            RightLightRate: "2.2%",
            RightMediumCount: 13,
            RightMediumRate: "1.4%",
            RightHeavyCount: 7,
            RightHeavyRate: "0.8%",
            hasChildren: false,
            children: [],
          },
        ],
      },
      {
        Id: "2",
        Name: "上海市实验小学",
        ScreeningCount: 2143,
        SpineCount: 98,
        SpineRate: "4.6%",
        LowRiskCount: 73,
        LowRiskRate: "3.4%",
        HighRiskCount: 25,
        HighRiskRate: "1.2%",
        FootCount: 189,
        FootRate: "8.8%",
        LeftHighCount: 42,
        LeftHighRate: "2.0%",
        LeftLightCount: 56,
        LeftLightRate: "2.6%",
        LeftMediumCount: 34,
        LeftMediumRate: "1.6%",
        LeftHeavyCount: 15,
        LeftHeavyRate: "0.7%",
        RightHighCount: 38,
        RightHighRate: "1.8%",
        RightLightCount: 52,
        RightLightRate: "2.4%",
        RightMediumCount: 31,
        RightMediumRate: "1.4%",
        RightHeavyCount: 17,
        RightHeavyRate: "0.8%",
        hasChildren: true,
        children: [
          {
            Id: "2-1",
            Name: "男",
            ScreeningCount: 1089,
            SpineCount: 50,
            SpineRate: "4.6%",
            LowRiskCount: 37,
            LowRiskRate: "3.4%",
            HighRiskCount: 13,
            HighRiskRate: "1.2%",
            FootCount: 96,
            FootRate: "8.8%",
            LeftHighCount: 21,
            LeftHighRate: "1.9%",
            LeftLightCount: 28,
            LeftLightRate: "2.6%",
            LeftMediumCount: 17,
            LeftMediumRate: "1.6%",
            LeftHeavyCount: 8,
            LeftHeavyRate: "0.7%",
            RightHighCount: 19,
            RightHighRate: "1.7%",
            RightLightCount: 26,
            RightLightRate: "2.4%",
            RightMediumCount: 15,
            RightMediumRate: "1.4%",
            RightHeavyCount: 9,
            RightHeavyRate: "0.8%",
            hasChildren: false,
            children: [],
          },
          {
            Id: "2-2",
            Name: "女",
            ScreeningCount: 1054,
            SpineCount: 48,
            SpineRate: "4.6%",
            LowRiskCount: 36,
            LowRiskRate: "3.4%",
            HighRiskCount: 12,
            HighRiskRate: "1.1%",
            FootCount: 93,
            FootRate: "8.8%",
            LeftHighCount: 21,
            LeftHighRate: "2.0%",
            LeftLightCount: 28,
            LeftLightRate: "2.7%",
            LeftMediumCount: 17,
            LeftMediumRate: "1.6%",
            LeftHeavyCount: 7,
            LeftHeavyRate: "0.7%",
            RightHighCount: 19,
            RightHighRate: "1.8%",
            RightLightCount: 26,
            RightLightRate: "2.5%",
            RightMediumCount: 16,
            RightMediumRate: "1.5%",
            RightHeavyCount: 8,
            RightHeavyRate: "0.8%",
            hasChildren: false,
            children: [],
          },
        ],
      },
      {
        Id: "3",
        Name: "广州市天河小学",
        ScreeningCount: 1634,
        SpineCount: 72,
        SpineRate: "4.4%",
        LowRiskCount: 54,
        LowRiskRate: "3.3%",
        HighRiskCount: 18,
        HighRiskRate: "1.1%",
        FootCount: 142,
        FootRate: "8.7%",
        LeftHighCount: 31,
        LeftHighRate: "1.9%",
        LeftLightCount: 38,
        LeftLightRate: "2.3%",
        LeftMediumCount: 24,
        LeftMediumRate: "1.5%",
        LeftHeavyCount: 11,
        LeftHeavyRate: "0.7%",
        RightHighCount: 29,
        RightHighRate: "1.8%",
        RightLightCount: 36,
        RightLightRate: "2.2%",
        RightMediumCount: 22,
        RightMediumRate: "1.3%",
        RightHeavyCount: 13,
        RightHeavyRate: "0.8%",
        hasChildren: true,
        children: [
          {
            Id: "3-1",
            Name: "男",
            ScreeningCount: 831,
            SpineCount: 37,
            SpineRate: "4.5%",
            LowRiskCount: 27,
            LowRiskRate: "3.2%",
            HighRiskCount: 10,
            HighRiskRate: "1.2%",
            FootCount: 72,
            FootRate: "8.7%",
            LeftHighCount: 16,
            LeftHighRate: "1.9%",
            LeftLightCount: 19,
            LeftLightRate: "2.3%",
            LeftMediumCount: 12,
            LeftMediumRate: "1.4%",
            LeftHeavyCount: 6,
            LeftHeavyRate: "0.7%",
            RightHighCount: 15,
            RightHighRate: "1.8%",
            RightLightCount: 18,
            RightLightRate: "2.2%",
            RightMediumCount: 11,
            RightMediumRate: "1.3%",
            RightHeavyCount: 7,
            RightHeavyRate: "0.8%",
            hasChildren: false,
            children: [],
          },
          {
            Id: "3-2",
            Name: "女",
            ScreeningCount: 803,
            SpineCount: 35,
            SpineRate: "4.4%",
            LowRiskCount: 27,
            LowRiskRate: "3.4%",
            HighRiskCount: 8,
            HighRiskRate: "1.0%",
            FootCount: 70,
            FootRate: "8.7%",
            LeftHighCount: 15,
            LeftHighRate: "1.9%",
            LeftLightCount: 19,
            LeftLightRate: "2.4%",
            LeftMediumCount: 12,
            LeftMediumRate: "1.5%",
            LeftHeavyCount: 5,
            LeftHeavyRate: "0.6%",
            RightHighCount: 14,
            RightHighRate: "1.7%",
            RightLightCount: 18,
            RightLightRate: "2.2%",
            RightMediumCount: 11,
            RightMediumRate: "1.4%",
            RightHeavyCount: 6,
            RightHeavyRate: "0.7%",
            hasChildren: false,
            children: [],
          },
        ],
      },
      {
        Id: "4",
        Name: "深圳市南山小学",
        ScreeningCount: 1923,
        SpineCount: 84,
        SpineRate: "4.4%",
        LowRiskCount: 62,
        LowRiskRate: "3.2%",
        HighRiskCount: 22,
        HighRiskRate: "1.1%",
        FootCount: 167,
        FootRate: "8.7%",
        LeftHighCount: 36,
        LeftHighRate: "1.9%",
        LeftLightCount: 48,
        LeftLightRate: "2.5%",
        LeftMediumCount: 29,
        LeftMediumRate: "1.5%",
        LeftHeavyCount: 13,
        LeftHeavyRate: "0.7%",
        RightHighCount: 35,
        RightHighRate: "1.8%",
        RightLightCount: 44,
        RightLightRate: "2.3%",
        RightMediumCount: 27,
        RightMediumRate: "1.4%",
        RightHeavyCount: 15,
        RightHeavyRate: "0.8%",
        hasChildren: true,
        children: [
          {
            Id: "4-1",
            Name: "男",
            ScreeningCount: 977,
            SpineCount: 43,
            SpineRate: "4.4%",
            LowRiskCount: 31,
            LowRiskRate: "3.2%",
            HighRiskCount: 12,
            HighRiskRate: "1.2%",
            FootCount: 85,
            FootRate: "8.7%",
            LeftHighCount: 18,
            LeftHighRate: "1.8%",
            LeftLightCount: 24,
            LeftLightRate: "2.5%",
            LeftMediumCount: 15,
            LeftMediumRate: "1.5%",
            LeftHeavyCount: 7,
            LeftHeavyRate: "0.7%",
            RightHighCount: 18,
            RightHighRate: "1.8%",
            RightLightCount: 22,
            RightLightRate: "2.3%",
            RightMediumCount: 14,
            RightMediumRate: "1.4%",
            RightHeavyCount: 8,
            RightHeavyRate: "0.8%",
            hasChildren: false,
            children: [],
          },
          {
            Id: "4-2",
            Name: "女",
            ScreeningCount: 946,
            SpineCount: 41,
            SpineRate: "4.3%",
            LowRiskCount: 31,
            LowRiskRate: "3.3%",
            HighRiskCount: 10,
            HighRiskRate: "1.1%",
            FootCount: 82,
            FootRate: "8.7%",
            LeftHighCount: 18,
            LeftHighRate: "1.9%",
            LeftLightCount: 24,
            LeftLightRate: "2.5%",
            LeftMediumCount: 14,
            LeftMediumRate: "1.5%",
            LeftHeavyCount: 6,
            LeftHeavyRate: "0.6%",
            RightHighCount: 17,
            RightHighRate: "1.8%",
            RightLightCount: 22,
            RightLightRate: "2.3%",
            RightMediumCount: 13,
            RightMediumRate: "1.4%",
            RightHeavyCount: 7,
            RightHeavyRate: "0.7%",
            hasChildren: false,
            children: [],
          },
        ],
      },
      {
        Id: "5",
        Name: "杭州市西湖小学",
        ScreeningCount: 1455,
        SpineCount: 63,
        SpineRate: "4.3%",
        LowRiskCount: 48,
        LowRiskRate: "3.3%",
        HighRiskCount: 15,
        HighRiskRate: "1.0%",
        FootCount: 124,
        FootRate: "8.5%",
        LeftHighCount: 27,
        LeftHighRate: "1.9%",
        LeftLightCount: 34,
        LeftLightRate: "2.3%",
        LeftMediumCount: 21,
        LeftMediumRate: "1.4%",
        LeftHeavyCount: 9,
        LeftHeavyRate: "0.6%",
        RightHighCount: 26,
        RightHighRate: "1.8%",
        RightLightCount: 32,
        RightLightRate: "2.2%",
        RightMediumCount: 19,
        RightMediumRate: "1.3%",
        RightHeavyCount: 11,
        RightHeavyRate: "0.8%",
        hasChildren: true,
        children: [
          {
            Id: "5-1",
            Name: "男",
            ScreeningCount: 740,
            SpineCount: 32,
            SpineRate: "4.3%",
            LowRiskCount: 24,
            LowRiskRate: "3.2%",
            HighRiskCount: 8,
            HighRiskRate: "1.1%",
            FootCount: 63,
            FootRate: "8.5%",
            LeftHighCount: 14,
            LeftHighRate: "1.9%",
            LeftLightCount: 17,
            LeftLightRate: "2.3%",
            LeftMediumCount: 10,
            LeftMediumRate: "1.4%",
            LeftHeavyCount: 5,
            LeftHeavyRate: "0.7%",
            RightHighCount: 13,
            RightHighRate: "1.8%",
            RightLightCount: 16,
            RightLightRate: "2.2%",
            RightMediumCount: 10,
            RightMediumRate: "1.4%",
            RightHeavyCount: 6,
            RightHeavyRate: "0.8%",
            hasChildren: false,
            children: [],
          },
          {
            Id: "5-2",
            Name: "女",
            ScreeningCount: 715,
            SpineCount: 31,
            SpineRate: "4.3%",
            LowRiskCount: 24,
            LowRiskRate: "3.4%",
            HighRiskCount: 7,
            HighRiskRate: "1.0%",
            FootCount: 61,
            FootRate: "8.5%",
            LeftHighCount: 13,
            LeftHighRate: "1.8%",
            LeftLightCount: 17,
            LeftLightRate: "2.4%",
            LeftMediumCount: 11,
            LeftMediumRate: "1.5%",
            LeftHeavyCount: 4,
            LeftHeavyRate: "0.6%",
            RightHighCount: 13,
            RightHighRate: "1.8%",
            RightLightCount: 16,
            RightLightRate: "2.2%",
            RightMediumCount: 9,
            RightMediumRate: "1.3%",
            RightHeavyCount: 5,
            RightHeavyRate: "0.7%",
            hasChildren: false,
            children: [],
          },
        ],
      },
    ];

    rawData.value = mockData;
    total.value = mockData.length;
  } catch (error) {
    console.error("加载数据失败:", error);
    rawData.value = [];
    total.value = 0;
  } finally {
    tableLoading.value = false;
  }
};

const handleGetLabel = (): string => {
  if (queryParams.value.Class) {
    return "性别";
  } else if (queryParams.value.Grade) {
    return "班级";
  } else if (queryParams.value.School) {
    return "年级";
  } else {
    return "学校";
  }
};

watch(timeRange, (newVal) => {
  queryParams.value.QueryStartDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.QueryEndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
