<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                  :shortcuts="pageDatePickerShortcuts"
                />
              </el-form-item>

              <el-form-item label="学校">
                <el-input v-model="queryParams.School" placeholder="请输入" clearable />
              </el-form-item>
              <el-form-item label="年级">
                <el-select
                  v-model="queryParams.Grade"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="一年级" value="一年级" />
                  <el-option label="二年级" value="二年级" />
                  <el-option label="三年级" value="三年级" />
                  <el-option label="四年级" value="四年级" />
                  <el-option label="五年级" value="五年级" />
                  <el-option label="六年级" value="六年级" />
                </el-select>
              </el-form-item>
              <el-form-item label="班级">
                <el-input v-model="queryParams.Class" placeholder="请输入" clearable />
              </el-form-item>
              <el-form-item label="关键字搜索" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="姓名/电话号码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="flex: 1; text-align: center"
        >
          <el-table-column prop="Name" label="姓名" align="center" />
          <el-table-column prop="Class" label="班级" align="center">
            <template #default="scope">
              <div>{{ scope.row.School + "/" + scope.row.Grade + "/" + scope.row.Class }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="Sex" label="性别" align="center" />
          <el-table-column prop="Birthday" label="生日" align="center">
            <template #default="scope">
              <div>{{ scope.row.Birthday }}</div>
              <span class="ml-2px">{{ "(" + scope.row.Age + "岁)" }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="Phone" label="电话" align="center" />
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handleSeeDetail(scope.row, 'spine')">
                筛查脊柱
              </el-button>
              <el-button link type="primary" @click="handleSeeDetail(scope.row, 'foot')">
                足底筛查
              </el-button>
              <el-button link type="primary" @click="handleSeeDetail(scope.row, '3d')">
                3D体态检测
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog v-model="dialogVisible" title="学生详情" width="1100" destroy-on-close>
      <StudentContent />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";
import { GlobalStatisticItem, GlobalStatisticsInputDTO } from "@/api/supplier-common/types";
import Supplier_Common_Api from "@/api/supplier-common";
import StudentContent from "./components/StudentContent.vue";
const { datePickerShortcuts } = useDateRangePicker();
const pageDatePickerShortcuts = [
  ...datePickerShortcuts,
  {
    text: "2025年上半年",
    value() {
      const end = "2025-06-30 23:59:59";
      const start = "2025-05-26 00:00:00";
      return [start, end];
    },
  },
];

defineOptions({
  name: "SpinalScreeningOverallData",
});

const queryParams = ref<GlobalStatisticsInputDTO>({
  PageIndex: 1,
  PageSize: 20,
  StartDate: dayjs().format("YYYY-MM-01 00:00:00"),
  EndDate: dayjs().format("YYYY-MM-DD 23:59:59"),
  School: null,
  DtoName: "ReportUser",
  Grade: null,
  Class: null,
  Keyword: "",
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const studentInfo = ref<{ name: string; phone: string; type: string }>({
  name: "",
  phone: "",
  type: "",
});
provide("studentInfo", studentInfo);

const dialogVisible = ref<boolean>(false);

const { tableLoading, pageData, total, tableRef, exportLoading, tableFluidHeight, tableResize } =
  useTableConfig<GlobalStatisticItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Supplier_Common_Api.globalStatistics(queryParams.value);
  if (res.Type === 200) {
    res.Data.Data.forEach((item) => {
      item.Birthday = dayjs(item.Birthday!).format("YYYY-MM-DD");
    });
    pageData.value = res.Data.Data;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

const handleSeeDetail = (row: GlobalStatisticItem, type: string) => {
  studentInfo.value = {
    name: row.Name!,
    phone: row.Phone!,
    type,
  };
  dialogVisible.value = true;
};

watch(timeRange, (newVal) => {
  queryParams.value.StartDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
