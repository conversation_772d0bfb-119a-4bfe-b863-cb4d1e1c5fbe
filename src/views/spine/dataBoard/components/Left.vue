<template>
  <div class="left-root">
    <!-- 上面四个数字 -->
    <div class="left-root-top">
      <div class="left-root-top-each">
        <span class="left-root-top-each-number">{{ pageData.topData.SchoolCount }}</span>
        <div class="left-root-top-each-title">已筛查学校</div>
      </div>
      <div class="left-root-top-each">
        <span class="left-root-top-each-number">{{ pageData.topData.UserCount }}</span>
        <div class="left-root-top-each-title">已筛查人数</div>
      </div>
      <div class="left-root-top-each">
        <span class="left-root-top-each-number">{{ pageData.topData.IkidCount }}</span>
        <div class="left-root-top-each-title">脊柱侧弯人数</div>
      </div>
      <div class="left-root-top-each">
        <span class="left-root-top-each-number">{{ pageData.topData.ProblemRate }}</span>
        <div class="left-root-top-each-title">脊柱侧弯率</div>
      </div>
    </div>
    <!-- 脊柱情况分布 -->
    <div class="left-root-distribution">
      <div class="h-56px" />
      <div class="left-root-distribution-container">
        <ECharts
          ref="spinalDistributionRef"
          :options="spinalDistributionOptions"
          width="142px"
          height="142px"
          :automatic-highlight="true"
        />
        <div class="left-root-distribution-container-mid">
          <div
            v-for="item in pageData.ikidData.SpinalDistributionData"
            :key="item.name"
            class="left-root-distribution-container-mid-item"
          >
            <div
              class="left-root-distribution-container-mid-item-color"
              :style="{ backgroundColor: item.color }"
            />
            <div class="left-root-distribution-container-mid-item-name">
              {{ item.name }}
            </div>
            <div
              class="left-root-distribution-container-mid-item-value"
              :style="{ color: item.color }"
            >
              {{ item.value }}
            </div>
            <div class="left-root-distribution-container-mid-item-percentage">
              {{ item.percentage }}
            </div>
          </div>
        </div>
        <div class="left-root-distribution-container-right">
          <div class="left-root-distribution-container-right-title">脊柱侧弯率：</div>
          <div class="left-root-distribution-container-right-content">
            <div class="left-root-distribution-container-right-content-item">
              <el-progress
                type="circle"
                :percentage="pageData.ikidData.SpinalGenderData.BoyRate"
                :stroke-width="12"
                striped
                striped-flow
              />
              <div class="left-root-distribution-container-right-content-item-text">男生</div>
            </div>
            <div class="left-root-distribution-container-right-content-item">
              <el-progress
                type="circle"
                :percentage="pageData.ikidData.SpinalGenderData.GirlRate"
                :stroke-width="12"
                striped
                striped-flow
              />
              <div class="left-root-distribution-container-right-content-item-text">女生</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 足弓情况分布 -->
    <div class="left-root-distribution left-root-distribution-arch mt-34px!">
      <div class="h-56px left-root-distribution-select">
        <div
          class="left-root-distribution-select-item"
          :class="{
            'left-root-distribution-select-select': leftOrRight === 'left',
            'left-root-distribution-select-normal': leftOrRight === 'right',
          }"
          @click="handleSelect('left')"
        >
          左
        </div>
        <div
          class="left-root-distribution-select-item"
          :class="{
            'left-root-distribution-select-select': leftOrRight === 'right',
            'left-root-distribution-select-normal': leftOrRight === 'left',
          }"
          @click="handleSelect('right')"
        >
          右
        </div>
      </div>
      <div class="left-root-distribution-container">
        <ECharts
          :options="footDistributionOptions"
          width="142px"
          height="142px"
          :automatic-highlight="true"
        />
        <div class="left-root-distribution-container-mid">
          <div
            v-for="item in pageData.footData.FootDistributionData"
            :key="item.name"
            class="left-root-distribution-container-mid-item"
          >
            <div
              class="left-root-distribution-container-mid-item-color"
              :style="{ backgroundColor: item.color }"
            />
            <div
              class="left-root-distribution-container-mid-item-name"
              :style="{ marginRight: item.name === '脊柱正常' ? '44px' : '0' }"
            >
              {{ item.name }}
            </div>
            <div
              class="left-root-distribution-container-mid-item-value"
              :style="{ color: item.color }"
            >
              {{ item.value }}
            </div>
            <div class="left-root-distribution-container-mid-item-percentage">
              {{ item.percentage }}
            </div>
          </div>
        </div>
        <div class="left-root-distribution-container-right">
          <div class="left-root-distribution-container-right-title">足弓异常率：</div>
          <div class="left-root-distribution-container-right-content">
            <div class="left-root-distribution-container-right-content-item">
              <el-progress
                type="circle"
                :percentage="pageData.footData.FootGenderData.BoyRate"
                :stroke-width="12"
                striped
                striped-flow
              />
              <div class="left-root-distribution-container-right-content-item-text">男生</div>
            </div>
            <div class="left-root-distribution-container-right-content-item">
              <el-progress
                type="circle"
                :percentage="pageData.footData.FootGenderData.GirlRate"
                :stroke-width="12"
                striped
                striped-flow
              />
              <div class="left-root-distribution-container-right-content-item-text">女生</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ECBasicOption } from "echarts/types/dist/shared";
import type { BoardLeftData, PageData, ChartParams } from "../types";

const leftOrRight = ref<"left" | "right">("left");

const pageData = ref<PageData>({
  topData: {
    SchoolCount: 0,
    UserCount: 0,
    IkidCount: 0,
    ProblemRate: "0%",
  },
  ikidData: {
    SpinalDistributionData: [],
    SpinalGenderData: {
      BoyRate: 0,
      GirlRate: 0,
    },
  },
  footData: {
    left: {
      FootDistributionData: [],
      FootGenderData: {
        BoyRate: 0,
        GirlRate: 0,
      },
    },
    right: {
      FootDistributionData: [],
      FootGenderData: {
        BoyRate: 0,
        GirlRate: 0,
      },
    },
    FootDistributionData: [],
    FootGenderData: {
      BoyRate: 0,
      GirlRate: 0,
    },
  },
});

// ECharts 配置选项
const spinalDistributionOptions = ref<ECBasicOption>({});
const footDistributionOptions = ref<ECBasicOption>({});

const spinalDistributionRef = useTemplateRef("spinalDistributionRef");
// 脊柱分布数据（提取到外部作用域）
const handleGetSpinalDistributionOptions = () => {
  // 计算百分比
  spinalDistributionOptions.value = {
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c}人 ({d}%)",
    },
    series: [
      {
        name: "",
        type: "pie",
        animationDurationUpdate: 400,
        animation: true,
        radius: ["54px", "65px"], // 内外半径，创建环形效果，中心留空显示文案
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 0,
          borderColor: "transparent",
          borderWidth: 0,
        },
        label: {
          show: false,
          position: "center",
          formatter: (params: ChartParams) => {
            // 显示当前鼠标悬停或选中项目的数据
            const currentData = params.data;
            return `{percentage_style|${currentData.percentage}}\n{name_style|${currentData.name}}`;
          },
          rich: {
            name_style: {
              fontSize: 14,
              fontWeight: 400,
              color: "##A1B7C3",
              lineHeight: 16,
              marginTop: 10,
            },
            percentage_style: {
              fontSize: 24,
              fontWeight: "bold",
              color: "#A1B7C3",
              fontFamily: "fontNumber, sans-serif",
              lineHeight: 20,
            },
          },
        },
        labelLine: {
          show: false,
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "14",
            fontWeight: "normal",
          },
        },
        color: ["#FFAC26", "#00D4FF", "#0066CC"], // 橙色(脊柱正常)、浅蓝色(低风险)、深蓝色(高风险)
        data: pageData.value.ikidData.SpinalDistributionData,
      },
    ],
  };
};
//
const handleGetFootDistributionOptions = () => {
  // 计算百分比
  footDistributionOptions.value = {
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c}人 ({d}%)",
    },
    series: [
      {
        name: "",
        type: "pie",
        animationDurationUpdate: 400,
        animation: true,
        radius: ["54px", "65px"], // 内外半径，创建环形效果，中心留空显示文案
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 0,
          borderColor: "transparent",
          borderWidth: 0,
        },
        label: {
          show: false,
          position: "center",
          formatter: (params: ChartParams) => {
            // 显示当前鼠标悬停或选中项目的数据
            const currentData = params.data;
            return `{percentage_style|${currentData.percentage}}\n{name_style|${currentData.name}}`;
          },
          rich: {
            name_style: {
              fontSize: 14,
              fontWeight: 400,
              color: "##A1B7C3",
              lineHeight: 16,
              marginTop: 10,
            },
            percentage_style: {
              fontSize: 24,
              fontWeight: "bold",
              color: "#A1B7C3",
              fontFamily: "fontNumber, sans-serif",
              lineHeight: 20,
            },
          },
        },
        labelLine: {
          show: false,
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "14",
            fontWeight: "normal",
          },
        },
        color: ["#FFAC26", "#00D4FF", "#0066CC"], // 橙色(脊柱正常)、浅蓝色(低风险)、深蓝色(高风险)
        data: pageData.value.footData.FootDistributionData,
      },
    ],
  };
};

const handleProcessData = (newVal: BoardLeftData) => {
  // 设置顶部数据
  pageData.value.topData = newVal.topData;
  // 设置脊柱分布数据
  pageData.value.ikidData = newVal.ikidData;
  // 设置足弓分布数据
  pageData.value.footData = newVal.footData;
  // 设置脊柱分布数据
  handleGetSpinalDistributionOptions();
  // 设置足弓分布数据
  handleGetFootDistributionOptions();
};

const handleSelect = (type: "left" | "right") => {
  leftOrRight.value = type;
  // 设置足弓分布数据
  pageData.value.footData.FootDistributionData =
    type === "left"
      ? pageData.value.footData.left.FootDistributionData
      : pageData.value.footData.right.FootDistributionData;
  pageData.value.footData.FootGenderData =
    type === "left"
      ? pageData.value.footData.left.FootGenderData
      : pageData.value.footData.right.FootGenderData;
};
interface Props {
  boardLeftData: BoardLeftData;
}
const props = defineProps<Props>();
watch(
  () => props.boardLeftData,
  (newVal) => {
    if (newVal && Object.keys(newVal).length) {
      // 处理数据
      handleProcessData(newVal);
    }
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
@font-face {
  font-family: fontNumber;
  font-style: normal;
  font-weight: normal;
  src: url("../assets/font/font-number.TTF") format("truetype");
}

.left-root {
  width: 853px;
  height: 100%;
  padding-top: 44px;

  &-top {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0 54px 0 35px;
    // gap: 16px;
    &-each {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 174px;
      height: 125px;
      background-image: url("../assets/images/number-bg.png");
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;

      &-number {
        margin-top: 42px;
        font-family: fontNumber, sans-serif;
        font-size: 32px;
        font-weight: 400;
        color: #fff;
        background: linear-gradient(0deg, rgb(119 244 245 / 99%) 1.29%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      &-title {
        margin-top: auto;
        font-size: 14px;
        font-weight: bold;
        color: #fff;
      }
    }
  }

  &-distribution {
    display: flex;
    flex-direction: column;
    width: 853px;
    height: 306px;
    margin-top: 51px;
    background-image: url("../assets/images/distribution-bg.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;

    &-container {
      box-sizing: border-box;
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      height: 100%;
      padding-left: 60px;

      &-mid {
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: center;
        width: 100%;
        height: 100%;
        margin-left: 57px;

        &-item {
          display: flex;
          gap: 20px;
          align-items: center;
          width: 100%;

          &-name {
            width: 98px;
            font-size: 14px;
            font-weight: 400;
            color: #fff;
            text-align: left;
          }

          &-value {
            width: 54px;
            text-align: left;
          }

          &-color {
            width: 10px;
            height: 10px;
          }

          &-percentage {
            width: 54px;
            font-size: 18px;
            font-weight: 400;
            color: #fff;
            text-align: left;
          }
        }
      }

      &-right {
        width: 192px;
        margin-right: 55px;

        &-title {
          margin-bottom: 30px;
          font-size: 18px;
          font-weight: 400;
          color: #dffdfe;
        }

        &-content {
          display: flex;
          gap: 43px;
          align-items: center;
          justify-content: space-between;

          &-item {
            &-text {
              margin-top: 10px;
              font-size: 14px;
              font-weight: 400;
              color: #a5b2c8;
              text-align: center;
            }
          }
        }
      }
    }

    &-select {
      display: flex;
      align-items: flex-end;
      justify-content: flex-end;
      margin-right: 47px;

      &-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        font-size: 14px;
        font-weight: 400;
        color: #fff;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
      }

      &-item:hover {
        cursor: pointer;
      }

      &-select {
        background-image: url("../assets/images/icon-select.png");
      }

      &-normal {
        background-image: url("../assets/images/icon-normal.png");
      }
    }
  }

  &-distribution-arch {
    background-image: url("../assets/images/arch-bg.png");
  }
}

.distribution-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.distribution-item {
  display: flex;
  gap: 10px;
  align-items: center;
  font-size: 14px;
  color: #fff;
}

.distribution-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;

  &.normal {
    background-color: #00d699;
  }

  &.low-risk {
    background-color: #ffa726;
  }

  &.high-risk {
    background-color: #ff5252;
  }
}

:deep(.el-progress-circle) {
  width: 76px !important;
  height: 76px !important;
}

:deep(.el-progress__text) {
  font-size: 20px !important;
  font-weight: 400 !important;
  color: #fff !important;
}
</style>
