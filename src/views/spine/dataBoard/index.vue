<template>
  <div ref="fullscreenDiv" v-loading="pageLoading" class="page-root">
    <VScaleScreen>
      <div class="screen-root">
        <!-- 头部 -->
        <div class="screen-root-top">
          <!-- 全屏按钮 -->
          <div
            :class="`i-svg:${isFullscreen ? 'fullscreen1-exit' : 'fullscreen1'} c-white absolute top-10px right-10px`"
            @click="toggle"
          />
        </div>
        <!-- 筛选 -->
        <div class="screen-root-filter">
          <span class="screen-root-filter-label">筛查时间</span>
          <el-date-picker
            v-model="timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            unlink-panels
            :teleported="false"
            :shortcuts="pageDatePickerShortcuts"
            style="width: 220px !important; height: 40px"
            :clearable="false"
          />
        </div>
        <!-- 中间 -->
        <div class="screen-root-mid c-white">
          <Left :board-left-data="boardLeftData" />
          <Right :board-right-data="boardRightData" />
        </div>
      </div>
    </VScaleScreen>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();
import Left from "./components/Left.vue";
import Right from "./components/Right.vue";
import Supplier_Common_Api from "@/api/supplier-common";
import { FootData, IkidAndFootItem, IkidData } from "@/api/supplier-common/types";
import type {
  BoardLeftData,
  BoardRightData,
  DistributionItem,
  TopData,
  SpinalData,
  FootArchData,
} from "./types";

const { isFullscreen, toggle } = useFullscreen(useTemplateRef<HTMLDivElement>("fullscreenDiv"));
defineOptions({
  name: "SpinalScreeningDataBoard",
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const pageDatePickerShortcuts = [
  ...datePickerShortcuts,
  {
    text: "2025年上半年",
    value() {
      const end = "2025-06-30 23:59:59";
      const start = "2025-05-26 00:00:00";
      return [start, end];
    },
  },
];

const pageLoading = ref<boolean>(false);

const queryParams = ref({
  StartDate: dayjs().format("YYYY-MM-01 00:00:00"),
  EndDate: dayjs().format("YYYY-MM-DD 23:59:59"),
});

const boardLeftData = ref<BoardLeftData>({} as BoardLeftData);
const boardRightData = ref<BoardRightData>({} as BoardRightData);

const handleGetSpineData = async () => {
  pageLoading.value = true;
  const res = await Supplier_Common_Api.ikidAndFootStatistics(queryParams.value);
  if (res.Type === 200) {
    // 组装左侧数据
    handleProcessLeftData(res.Data);
    // 组装右侧数据
    handleProcessRightData(res.Data);
  }
  pageLoading.value = false;
};
// 创建分布数据项的辅助函数
const createDistributionItem = (
  value: number | undefined,
  name: string,
  percentage: string | undefined,
  color: string,
  needPercentSuffix = false
): DistributionItem => ({
  value: value || 0,
  name,
  percentage: needPercentSuffix ? (percentage || "0") + "%" : percentage || "0%",
  color,
  selected: name === "脊柱正常",
});

const createSpinalDistributionData = (ikidData: IkidData): DistributionItem[] => [
  createDistributionItem(ikidData?.NormalCount, "脊柱正常", ikidData?.NormalRate, "#FFAC26", true),
  createDistributionItem(ikidData?.LowCount, "脊椎侧弯低风险", ikidData?.LowRate, "#00D4FF", true),
  createDistributionItem(
    ikidData?.HighCount,
    "脊椎侧弯高风险",
    ikidData?.HighRate,
    "#0066CC",
    true
  ),
];

// 获取足弓字段值的辅助函数
const getFootFieldValue = <T,>(
  footData: FootData,
  type: "left" | "right",
  leftField: keyof FootData,
  rightField: keyof FootData
): T | undefined => {
  return type === "left" ? (footData[leftField] as T) : (footData[rightField] as T);
};

const createFootDistributionData = (
  footData: FootData,
  type: "left" | "right"
): DistributionItem[] => {
  const normalCount = getFootFieldValue<number>(
    footData,
    type,
    "LeftNormalCount",
    "RightNormalCount"
  );
  const normalRate = getFootFieldValue<string>(footData, type, "LeftNormalRate", "RightNormalRate");
  const lowCount = getFootFieldValue<number>(footData, type, "LeftLowCount", "RightLowCount");
  const lowRate = getFootFieldValue<string>(footData, type, "LeftLowRate", "RightLowRate");
  const highCount = getFootFieldValue<number>(footData, type, "LeftHighCount", "RightHighCount");
  const highRate = getFootFieldValue<string>(footData, type, "LeftHighRate", "RightHighRate");

  return [
    createDistributionItem(normalCount, "足弓正常", normalRate, "#FFAC26", true),
    createDistributionItem(lowCount, "低足弓", lowRate, "#00D4FF", true),
    createDistributionItem(highCount, "高足弓", highRate, "#0066CC", true),
  ];
};

// 创建头部数据
const createTopData = (info: IkidAndFootItem): TopData => ({
  SchoolCount: info.SchoolCount || 0,
  UserCount: info.UserCount || 0,
  IkidCount: info.IkidData?.UserCount || 0,
  ProblemRate: (info.IkidData?.ProblemRate || "0") + "%",
});

// 创建脊柱数据
const createIkidData = (info: IkidAndFootItem): SpinalData => ({
  SpinalDistributionData: createSpinalDistributionData(info.IkidData || {}),
  SpinalGenderData: {
    BoyRate: Number(info.IkidData?.BoyRate || 0),
    GirlRate: Number(info.IkidData?.GirlRate || 0),
  },
});

// 创建性别数据
const createGenderData = (boyRate?: string, girlRate?: string) => ({
  BoyRate: Number(boyRate || 0),
  GirlRate: Number(girlRate || 0),
});

// 创建足弓数据
const createFootData = (info: IkidAndFootItem): FootArchData => {
  const footData = info.FootData || {};
  const leftGenderData = createGenderData(info.FootData?.LeftBoyRate, info.FootData?.LeftGirlRate);
  const rightGenderData = createGenderData(
    info.FootData?.RightBoyRate,
    info.FootData?.RightGirlRate
  );

  return {
    left: {
      FootDistributionData: createFootDistributionData(footData, "left"),
      FootGenderData: leftGenderData,
    },
    right: {
      FootDistributionData: createFootDistributionData(footData, "right"),
      FootGenderData: rightGenderData,
    },
    FootDistributionData: createFootDistributionData(footData, "left"),
    FootGenderData: leftGenderData,
  };
};

const handleProcessLeftData = (info: IkidAndFootItem) => {
  const leftData: BoardLeftData = {
    topData: createTopData(info),
    ikidData: createIkidData(info),
    footData: createFootData(info),
  };
  boardLeftData.value = leftData;
};

// 处理单个方向的图表数据
const processChartData = (charts: any[] | undefined) => {
  if (!charts) {
    return {
      x: [],
      footLightData: [],
      footModerateData: [],
      footSevereData: [],
      footHighData: [],
    };
  }

  return {
    x: charts.map((s) => s.Age! + "岁"),
    footLightData: charts.map((s) => Number(s.MildRate!)),
    footModerateData: charts.map((s) => Number(s.Moderate!)),
    footSevereData: charts.map((s) => Number(s.Severe!)),
    footHighData: charts.map((s) => Number(s.HighFoot!)),
  };
};

const handleProcessRightData = (info: IkidAndFootItem) => {
  const rightData = {
    footData: {
      left: processChartData(info.FootData?.LeftLineCharts),
      right: processChartData(info.FootData?.RightLineCharts),
    },
    ikidData: {
      ageGroups: info.IkidData?.HighLineCharts?.map((s) => s.Age! + "岁")!,
      lowRiskData: info.IkidData?.LowLineCharts?.map((s) => Number(s.Rate!))!,
      highRiskData: info.IkidData?.HighLineCharts?.map((s) => Number(s.Rate!))!,
    },
  };
  boardRightData.value = rightData;
};

watch(timeRange, (newVal) => {
  queryParams.value.StartDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
  handleGetSpineData();
});
onActivated(() => {
  handleGetSpineData();
});
</script>

<style lang="scss" scoped>
.page-root {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.screen-root {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  width: 1920px;
  height: 1080px;
  background-image: url("./assets/images/bg.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;

  &-filter {
    display: flex;
    align-items: center;
    width: 330px;
    height: 40px;
    padding: 0 78px;

    &-label {
      margin-right: 16px;
      font-size: 14px;
      font-weight: 400;
      color: #fff;
    }
  }

  &-top {
    position: relative;
    height: 110px;
    background-image: url("./assets/topLogo.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;

    &-date-time {
      position: absolute;
      top: 30px;
      right: 10px;
      display: flex;
      align-items: center;

      .time {
        font-size: 24px;
        font-weight: 500;
        color: #fff;
      }

      .date {
        margin-left: 12px;
        font-size: 12px;
        font-weight: 400;
        color: rgb(255 255 255 / 80%);
      }
    }
  }

  &-mid {
    display: flex;
    flex: 1;
    align-items: stretch;
    padding: 0 78px 64px;
  }
}

:deep(.el-date-editor) {
  color: #aaa !important;
  background: rgb(30 231 231 / 5%) !important;
  border: 1px solid #1ee7e73b !important;
  border-radius: 4px !important;
}

:deep(.el-range-input) {
  color: #aaa !important;
}

:deep(.el-range-separator) {
  color: #aaa !important;
}
</style>
